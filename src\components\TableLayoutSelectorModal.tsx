import React, { useState, useEffect } from 'react';
import { X, RefreshCw, Eye } from 'lucide-react';
import { TableLayout, LayoutTable, DiningTable } from '../types';
import { getAllLayouts, getActiveLayout } from '../services/layoutService';
import { getActiveDiningTables, getTableCurrentOrder, initializeDefaultTables } from '../services/tableService';
import { dbService } from '../services/indexedDBService';

interface TableLayoutSelectorModalProps {
  isOpen: boolean;
  onSelectTable: (tableId: number) => void;
  onClose: () => void;
}

interface TableWithStatus extends LayoutTable {
  diningTableId: number;
  realStatus: DiningTable['status'];
  hasOrder: boolean;
  currentOrderId?: string;
}

interface LayoutViewerProps {
  layout: TableLayout | null;
  tablesWithStatus: TableWithStatus[];
  onTableClick: (table: TableWithStatus) => void;
}

const LayoutViewer: React.FC<LayoutViewerProps> = ({ layout, tablesWithStatus, onTableClick }) => {
  if (!layout) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <Eye className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-xl">No layout available</p>
          <p className="text-sm">Please create a layout first</p>
        </div>
      </div>
    );
  }

  const getTableStatusColor = (table: TableWithStatus) => {
    switch (table.realStatus) {
      case 'available': return '#16a34a'; // green
      case 'occupied': return '#dc2626'; // red
      case 'reserved': return '#f59e0b'; // yellow
      default: return table.borderColor;
    }
  };

  const getTableStatusBg = (table: TableWithStatus) => {
    switch (table.realStatus) {
      case 'available': return 'bg-green-50';
      case 'occupied': return 'bg-red-50';
      case 'reserved': return 'bg-yellow-50';
      default: return 'bg-white';
    }
  };

  return (
    <div className="flex-1 relative overflow-auto bg-gray-50">
      <div
        className="min-w-full min-h-full relative"
        style={{
          backgroundImage: layout.floorPlanImage ? `url(${layout.floorPlanImage})` : 'none',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          minHeight: '600px',
          minWidth: '800px'
        }}
      >
        {/* Grid overlay when no image */}
        {!layout.floorPlanImage && (
          <div 
            className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0,0,0,0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.3) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />
        )}

        {/* Tables */}
        {tablesWithStatus.map(table => (
          <div
            key={table.id}
            className="absolute cursor-pointer select-none z-10 hover:z-20 transition-all hover:scale-105 group"
            style={{
              left: table.x + 40,
              top: table.y + 40,
              width: table.width,
              height: table.height,
            }}
            onClick={() => onTableClick(table)}
            title={`Table ${table.number} - ${table.realStatus}${table.hasOrder ? ' (Has Order)' : ''}`}
          >
            {/* Table */}
            <div
              className={`w-full h-full rounded-lg border-4 flex items-center justify-center
                         font-bold text-lg shadow-lg bg-opacity-90 backdrop-blur-sm
                         hover:shadow-xl hover:bg-opacity-95 hover:border-opacity-80 transition-all
                         group-hover:scale-105 ${getTableStatusBg(table)}
                         ${table.hasOrder ? 'ring-2 ring-blue-300 ring-opacity-50' : ''}`}
              style={{
                borderColor: getTableStatusColor(table),
                color: getTableStatusColor(table)
              }}
            >
              <div className="text-center">
                <div className="text-xl font-bold">{table.number}</div>
                <div className="text-xs opacity-80">{table.seats} seats</div>
                {table.hasOrder && (
                  <div className="text-xs mt-1 px-2 py-1 bg-white bg-opacity-70 rounded">
                    📋 Order
                  </div>
                )}
                {table.realStatus === 'occupied' && !table.hasOrder && (
                  <div className="text-xs mt-1 px-2 py-1 bg-white bg-opacity-70 rounded">
                    🔒 Occupied
                  </div>
                )}
                {table.realStatus === 'reserved' && (
                  <div className="text-xs mt-1 px-2 py-1 bg-white bg-opacity-70 rounded">
                    📅 Reserved
                  </div>
                )}
              </div>
            </div>

            {/* Status indicator */}
            <div 
              className="absolute -top-2 -left-2 w-4 h-4 rounded-full border-2 border-white shadow-sm"
              style={{ backgroundColor: getTableStatusColor(table) }}
            />
          </div>
        ))}

        {/* Layout info overlay */}
        {/* <div className="absolute top-4 left-4 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-md">
          <h3 className="font-bold text-gray-800">{layout.name}</h3>
          <p className="text-sm text-gray-600">{tablesWithStatus.length} tables</p>
          <div className="flex items-center space-x-4 mt-2 text-xs">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span>Available</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span>Occupied</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span>Reserved</span>
            </div>
          </div>
        </div> */}
      </div>
    </div>
  );
};

export function TableLayoutSelectorModal({ isOpen, onSelectTable, onClose }: TableLayoutSelectorModalProps) {
  const [layouts, setLayouts] = useState<TableLayout[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [tablesWithStatus, setTablesWithStatus] = useState<TableWithStatus[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  useEffect(() => {
    if (activeTab) {
      loadTablesForLayout(activeTab);
    }
  }, [activeTab]);

  const loadData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading layouts and active layout...');

      // Try to load layouts first
      let savedLayouts: TableLayout[] = [];
      try {
        savedLayouts = await getAllLayouts();
        console.log('Layouts loaded successfully:', savedLayouts);

        // If no layouts exist, try to initialize default tables
        if (savedLayouts.length === 0) {
          console.log('No layouts found, initializing default tables...');
          await initializeDefaultTables();
          savedLayouts = await getAllLayouts();
          console.log('Layouts after initialization:', savedLayouts);
        }
      } catch (layoutError) {
        console.error('Error loading layouts:', layoutError);
        throw new Error(`Failed to load layouts: ${layoutError instanceof Error ? layoutError.message : 'Unknown error'}`);
      }

      // Try to get active layout (this might fail due to IndexedDB issues)
      let activeLayout: TableLayout | null = null;
      try {
        activeLayout = await getActiveLayout();
        console.log('Active layout loaded:', activeLayout);
      } catch (activeLayoutError) {
        console.warn('Error loading active layout (will continue without it):', activeLayoutError);
        // Don't throw here, we can continue without active layout
      }

      const sortedLayouts = savedLayouts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      setLayouts(sortedLayouts);

      // Set active layout as default, or first layout if no active layout
      const defaultTab = activeLayout?.id || (sortedLayouts.length > 0 ? sortedLayouts[0].id : null);
      if (defaultTab) {
        setActiveTab(defaultTab);
        console.log('Set default tab to:', defaultTab);
      } else {
        console.warn('No layouts available to set as default tab');
      }
    } catch (err) {
      console.error('Failed to load layouts:', err);
      setError(`Failed to load layouts: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTablesForLayout = async (layoutId: string) => {
    try {
      const layout = layouts.find(l => l.id === layoutId);
      if (!layout) {
        console.warn('Layout not found:', layoutId);
        setTablesWithStatus([]);
        return;
      }

      console.log('Loading tables for layout:', layout.name);
      const diningTables = await getActiveDiningTables();
      console.log('Dining tables loaded:', diningTables);

      // Map layout tables to dining tables and get their status
      const tablesWithStatusPromises = layout.tables.map(async (layoutTable) => {
        const diningTable = diningTables.find(dt => dt.number === layoutTable.number);

        if (diningTable) {
          const hasOrder = !!diningTable.currentOrderId;
          console.log(`Table ${layoutTable.number}: status=${diningTable.status}, hasOrder=${hasOrder}`);
          return {
            ...layoutTable,
            diningTableId: diningTable.id,
            realStatus: diningTable.status,
            hasOrder,
            currentOrderId: diningTable.currentOrderId
          } as TableWithStatus;
        } else {
          // If no dining table found, create a virtual one
          console.log(`Table ${layoutTable.number}: no dining table found, creating virtual table`);
          return {
            ...layoutTable,
            diningTableId: layoutTable.id,
            realStatus: 'available' as const,
            hasOrder: false
          } as TableWithStatus;
        }
      });

      const tablesWithStatusData = await Promise.all(tablesWithStatusPromises);
      console.log('Tables with status:', tablesWithStatusData);
      setTablesWithStatus(tablesWithStatusData);
    } catch (err) {
      console.error('Failed to load table statuses:', err);
      setError('Failed to load table statuses. Please try refreshing.');
    }
  };

  const handleTableClick = async (table: TableWithStatus) => {
    try {
      console.log('Table selected:', {
        tableNumber: table.number,
        diningTableId: table.diningTableId,
        status: table.realStatus,
        hasOrder: table.hasOrder,
        currentOrderId: table.currentOrderId
      });

      // If table is occupied and has an order, we'll let the POS page handle loading the order
      // The POS page's handleTableSelect function already handles this case
      onSelectTable(table.diningTableId);
    } catch (err) {
      console.error('Failed to select table:', err);
    }
  };

  const debugDatabase = async () => {
    try {
      console.log('=== Database Debug ===');
      await dbService.init();
      await dbService.debugDatabaseSchema();

      console.log('Testing getAllLayouts...');
      const layouts = await getAllLayouts();
      console.log('Layouts:', layouts);

      console.log('Testing getActiveLayout...');
      const activeLayout = await getActiveLayout();
      console.log('Active layout:', activeLayout);

      console.log('Testing initializeDefaultTables...');
      await initializeDefaultTables();

      console.log('Re-testing getAllLayouts after initialization...');
      const layoutsAfter = await getAllLayouts();
      console.log('Layouts after init:', layoutsAfter);

    } catch (err) {
      console.error('Database debug failed:', err);
    }
  };

  const resetDatabase = async () => {
    try {
      console.log('=== Resetting Database ===');
      await dbService.resetDatabase();
      console.log('Database reset complete, reinitializing...');
      await dbService.init();
      await initializeDefaultTables();
      console.log('Database reinitialized, reloading data...');
      await loadData();
    } catch (err) {
      console.error('Database reset failed:', err);
    }
  };

  const activeLayout = layouts.find(layout => layout.id === activeTab);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[90vw] h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold">Select Table</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadData}
              disabled={isLoading}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            <p className="text-red-600 text-sm">{error}</p>
            <div className="mt-2 space-x-2">
              <button
                onClick={loadData}
                className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
              <button
                onClick={debugDatabase}
                className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
              >
                Debug DB
              </button>
              <button
                onClick={resetDatabase}
                className="px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700 transition-colors"
              >
                Reset DB
              </button>
            </div>
          </div>
        )}

        {/* Tabs */}
        {layouts.length > 0 && (
          <div className="bg-white border-b border-gray-200">
            <div className="flex overflow-x-auto">
              {layouts.map((layout) => (
                <button
                  key={layout.id}
                  onClick={() => setActiveTab(layout.id)}
                  className={`flex-shrink-0 px-6 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                    activeTab === layout.id
                      ? 'border-blue-500 text-blue-600 bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <span>{layout.name}</span>
                    <span className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                      {(layout.tables || []).length}
                    </span>
                    {layout.isActive && (
                      <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full">
                        Active
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 flex">
          {isLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <RefreshCw className="w-8 h-8 mx-auto mb-2 animate-spin text-gray-400" />
                <p className="text-gray-500">Loading layouts...</p>
              </div>
            </div>
          ) : (
            <LayoutViewer 
              layout={activeLayout || null} 
              tablesWithStatus={tablesWithStatus}
              onTableClick={handleTableClick}
            />
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              <div>Click on a table to select it for your order</div>
              <div className="text-xs text-gray-500 mt-1">
                📋 Tables with orders will load existing order • 🟢 Available • 🔴 Occupied • 🟡 Reserved
              </div>
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
