import React, { useState, useEffect } from 'react';
import { <PERSON>, RefreshCw, ArrowLeft, X } from 'lucide-react';
import { TableLayout, LayoutTable } from '../types';
import { getAllLayouts } from '../services/layoutService';

interface TablePopupProps {
  table: LayoutTable | null;
  onClose: () => void;
  position: { x: number; y: number };
}

interface LayoutViewerProps {
  layout: TableLayout | null;
  onTableClick: (table: LayoutTable, position: { x: number; y: number }) => void;
}

// Now using unified ResturantPOSDB via layoutService

const TablePopup: React.FC<TablePopupProps> = ({ table, onClose, position }) => {
  if (!table) return null;

  return (
    <div 
      className="fixed bg-white border-2 border-gray-300 rounded-lg shadow-lg p-4 z-50 min-w-48"
      style={{
        left: Math.min(position.x, window.innerWidth - 200),
        top: Math.min(position.y, window.innerHeight - 150),
      }}
    >
      <div className="flex justify-between items-start mb-3">
        <h3 className="font-bold text-lg text-gray-800">Table {table.number}</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Seats:</span>
          <span className="font-medium">{table.seats}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Status:</span>
          <span className={`font-medium capitalize px-2 py-1 rounded-full text-xs ${
            table.status === 'available' ? 'bg-green-100 text-green-800' :
            table.status === 'occupied' ? 'bg-red-100 text-red-800' :
            'bg-orange-100 text-orange-800'
          }`}>
            {table.status}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Size:</span>
          <span className="font-medium">{table.width} × {table.height}px</span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Color:</span>
          <div className="flex items-center space-x-2">
            <div 
              className="w-4 h-4 rounded border border-gray-300"
              style={{ backgroundColor: table.borderColor }}
            />
            <span className="font-medium text-xs font-mono">{table.borderColor}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const LayoutViewer: React.FC<LayoutViewerProps> = ({ layout, onTableClick }) => {
  if (!layout) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <Eye className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-xl">No layout selected</p>
          <p className="text-sm">Choose a tab to view a layout</p>
        </div>
      </div>
    );
  }

  const tables = layout.tables || [];

  return (
    <div className="flex-1 relative overflow-auto bg-gray-50">
      <div
        className="min-w-full min-h-full relative"
        style={{
          backgroundImage: layout.floorPlanImage ? `url(${layout.floorPlanImage})` : 'none',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'left top',
          minHeight: '800px',
          minWidth: '1200px'
        }}
      >
        {/* Grid overlay when no image */}
        {!layout.floorPlanImage && (
          <div 
            className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0,0,0,0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.3) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />
        )}

        {/* No floor plan message */}
        {!layout.floorPlanImage && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="text-center text-gray-400">
              <div className="text-xl mb-2">{layout.name}</div>
              <p className="text-sm">No floor plan image</p>
            </div>
          </div>
        )}

        {/* Tables */}
        {tables.map(table => (
          <div
            key={table.id}
            className="absolute cursor-pointer select-none z-10 hover:z-20 transition-all hover:scale-105"
            style={{
              left: table.x + 160,
              top: table.y,
              width: table.width,
              height: table.height,
            }}
            onClick={(e) => onTableClick(table, { x: e.clientX, y: e.clientY })}
          >
            {/* Table */}
            <div
              className="w-full h-full rounded-lg border-4 flex items-center justify-center
                         font-bold text-lg shadow-lg bg-white bg-opacity-80 backdrop-blur-sm
                         hover:shadow-xl hover:bg-opacity-90 transition-all"
              style={{
                borderColor: table.borderColor,
                color: table.borderColor
              }}
            >
              <div className="text-center">
                <div className="text-xl font-bold">{table.number}</div>
                <div className="text-xs opacity-80">{table.seats} seats</div>
              </div>
            </div>

            {/* Status indicator */}
            <div 
              className="absolute -top-1 -left-1 w-3 h-3 rounded-full border border-white shadow-sm"
              style={{ backgroundColor: table.borderColor }}
            />
          </div>
        ))}

        {/* Layout info overlay */}
        {/* <div className="absolute top-4 left-4 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-md">
          <h3 className="font-bold text-gray-800">{layout.name}</h3>
          <p className="text-sm text-gray-600">{tables.length} tables</p>
          <p className="text-xs text-gray-500">
            Created: {new Date(layout.createdAt).toLocaleDateString()}
          </p>
        </div> */}
      </div>
    </div>
  );
};

const LayoutViewerTabs: React.FC = () => {
  const [layouts, setLayouts] = useState<TableLayout[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTable, setSelectedTable] = useState<LayoutTable | null>(null);
  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadLayouts();
  }, []);

  const loadLayouts = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const savedLayouts = await getAllLayouts();
      const sortedLayouts = savedLayouts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      setLayouts(sortedLayouts);

      // Set first layout as active if available
      if (sortedLayouts.length > 0 && !activeTab) {
        setActiveTab(sortedLayouts[0].id);
      }
    } catch (err) {
      console.error('Failed to load layouts:', err);
      setError('Failed to load layouts. Please try refreshing the page.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTableClick = (table: LayoutTable, position: { x: number; y: number }) => {
    setSelectedTable(table);
    setPopupPosition(position);
  };

  const closePopup = () => {
    setSelectedTable(null);
  };

  const activeLayout = layouts.find(layout => layout.id === activeTab) || null;

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (selectedTable && !(e.target as Element).closest('.table-popup')) {
        closePopup();
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [selectedTable]);

  if (isLoading) {
    return (
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">Loading layouts...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
            <div className="text-red-600 mb-4">
              <X className="w-8 h-8 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Layouts</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={loadLayouts}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              <RefreshCw className="w-4 h-4 inline mr-2" />
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (layouts.length === 0) {
    return (
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <Eye className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">No Layouts Found</h2>
          <p className="text-gray-500 mb-6">You haven't saved any table layouts yet.</p>
          <p className="text-sm text-gray-400">Create and save layouts in the Table Layout Manager to view them here.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-100 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold text-gray-800">Layout Viewer</h1>
          <span className="text-sm text-gray-500">({layouts.length} layouts)</span>
        </div>
        
        <button
          onClick={loadLayouts}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Refresh</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="flex overflow-x-auto">
          {layouts.map((layout) => (
            <button
              key={layout.id}
              onClick={() => setActiveTab(layout.id)}
              className={`flex-shrink-0 px-6 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                activeTab === layout.id
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <span>{layout.name}</span>
                <span className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                  {(layout.tables || []).length}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex">
        <LayoutViewer 
          layout={activeLayout} 
          onTableClick={handleTableClick}
        />
      </div>

      {/* Table popup */}
      {selectedTable && (
        <div className="table-popup">
          <TablePopup
            table={selectedTable}
            onClose={closePopup}
            position={popupPosition}
          />
        </div>
      )}

      {/* Overlay to close popup */}
      {selectedTable && (
        <div 
          className="fixed inset-0 z-40"
          onClick={closePopup}
        />
      )}
    </div>
  );
};

export default LayoutViewerTabs;