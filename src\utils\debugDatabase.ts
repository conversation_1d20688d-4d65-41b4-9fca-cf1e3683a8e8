import { dbService } from '../services/indexedDBService';
import { getActiveLayout } from '../services/tableService';

// Debug utility to help diagnose IndexedDB issues
export class DatabaseDebugger {
  
  // Test the specific failing call
  static async testGetActiveLayout(): Promise<void> {
    console.log('=== Testing getActiveLayout function ===');
    
    try {
      // Initialize the database first
      await dbService.init();
      console.log('✓ Database initialized successfully');
      
      // Debug the database schema
      await dbService.debugDatabaseSchema();
      
      // Try to get active layout
      console.log('Attempting to get active layout...');
      const activeLayout = await getActiveLayout();
      console.log('✓ Active layout retrieved:', activeLayout);
      
    } catch (error) {
      console.error('✗ Error in getActiveLayout:', error);
      
      // Additional debugging
      console.log('Attempting direct dbService call...');
      try {
        const allLayouts = await dbService.getAll('tableLayouts');
        const activeLayouts = allLayouts.filter((layout: any) => layout.isActive === true);
        console.log('✓ Direct dbService call successful:', activeLayouts);
      } catch (directError) {
        console.error('✗ Direct dbService call failed:', directError);
      }
    }
  }
  
  // Test all table-related operations
  static async testTableOperations(): Promise<void> {
    console.log('=== Testing Table Operations ===');
    
    try {
      await dbService.init();
      
      // Test getting all table layouts
      console.log('Testing getAllTableLayouts...');
      const allLayouts = await dbService.getAll('tableLayouts');
      console.log('All layouts:', allLayouts);
      
      // Test getting by index (avoid boolean values)
      console.log('Testing manual filter for isActive...');
      const activeLayouts = allLayouts.filter((layout: any) => layout.isActive === true);
      console.log('Active layouts:', activeLayouts);
      
      // Test getting by index for name
      console.log('Testing getByIndex for name...');
      const namedLayouts = await dbService.getByIndex('tableLayouts', 'name', 'Default Layout');
      console.log('Named layouts:', namedLayouts);
      
    } catch (error) {
      console.error('Error in table operations:', error);
    }
  }
  
  // Reset and reinitialize database
  static async resetAndReinitialize(): Promise<void> {
    console.log('=== Resetting Database ===');
    
    try {
      // Reset the database
      await dbService.resetDatabase();
      console.log('✓ Database reset successfully');
      
      // Reinitialize
      await dbService.init();
      console.log('✓ Database reinitialized');
      
      // Check schema
      await dbService.debugDatabaseSchema();
      
    } catch (error) {
      console.error('Error resetting database:', error);
    }
  }
  
  // Create test data
  static async createTestLayout(): Promise<void> {
    console.log('=== Creating Test Layout ===');
    
    try {
      await dbService.init();
      
      const testLayout = {
        id: 'test-layout-' + Date.now(),
        name: 'Test Layout',
        isActive: true,
        tables: [
          { id: 1, x: 100, y: 100, width: 80, height: 80, number: 1, seats: 4, status: 'available', borderColor: '#16a34a' }
        ],
        floorPlanImage: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      await dbService.put('tableLayouts', testLayout);
      console.log('✓ Test layout created:', testLayout);
      
      // Verify it was created
      const retrieved = await dbService.get('tableLayouts', testLayout.id);
      console.log('✓ Test layout retrieved:', retrieved);
      
      // Test the manual filter query
      const allLayoutsAfter = await dbService.getAll('tableLayouts');
      const activeLayoutsAfter = allLayoutsAfter.filter((layout: any) => layout.isActive === true);
      console.log('✓ Active layouts after creation:', activeLayoutsAfter);
      
    } catch (error) {
      console.error('Error creating test layout:', error);
    }
  }
  
  // Run all tests
  static async runAllTests(): Promise<void> {
    console.log('=== Running All Database Tests ===');
    
    await this.testGetActiveLayout();
    await this.testTableOperations();
    await this.createTestLayout();
    await this.testGetActiveLayout(); // Test again after creating data
  }
}

// Make it available globally for browser console debugging
(window as any).DatabaseDebugger = DatabaseDebugger;

// Also make individual functions available for quick testing
(window as any).testDB = {
  init: () => dbService.init(),
  debug: () => dbService.debugDatabaseSchema(),
  reset: () => dbService.resetDatabase(),
  testActiveLayout: () => DatabaseDebugger.testGetActiveLayout(),
  runAll: () => DatabaseDebugger.runAllTests()
};

export default DatabaseDebugger;
