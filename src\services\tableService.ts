import { dbService } from './indexedDBService';
import { DiningTable, TableLayout, LayoutTable, Order, OrderItem } from '../types';

// Initialize table service
export async function initializeTableService(): Promise<void> {
  await dbService.init();
}

// ===== DINING TABLES (Operational Tables) =====

// Get all dining tables
export async function getAllDiningTables(): Promise<DiningTable[]> {
  await initializeTableService();
  return await dbService.getAll<DiningTable>('diningTables');
}

// Get dining tables for active layout
export async function getActiveDiningTables(): Promise<DiningTable[]> {
  await initializeTableService();
  const activeLayout = await getActiveLayout();
  if (!activeLayout) {
    return [];
  }
  return await dbService.getByIndex<DiningTable>('diningTables', 'layoutId', activeLayout.id);
}

// Get dining table by ID
export async function getDiningTableById(tableId: number): Promise<DiningTable | null> {
  await initializeTableService();
  return await dbService.get<DiningTable>('diningTables', tableId);
}

// Save dining table
export async function saveDiningTable(table: DiningTable): Promise<void> {
  await initializeTableService();
  await dbService.put('diningTables', table);
}

// Save multiple dining tables
export async function saveDiningTables(tables: DiningTable[]): Promise<void> {
  await initializeTableService();
  await dbService.bulkPut('diningTables', tables);
}

// Update dining table status
export async function updateDiningTableStatus(
  tableId: number,
  status: DiningTable['status'],
  orderId?: string
): Promise<void> {
  await initializeTableService();

  const table = await dbService.get<DiningTable>('diningTables', tableId);
  if (!table) {
    throw new Error('Dining table not found');
  }

  const updatedTable: DiningTable = {
    ...table,
    status,
    currentOrderId: orderId,
    lastOrderTime: orderId ? Date.now() : table.lastOrderTime
  };

  await dbService.put('diningTables', updatedTable);
}

// ===== TABLE LAYOUTS (Floor Plan Management) =====

// Get all table layouts
export async function getAllTableLayouts(): Promise<TableLayout[]> {
  await initializeTableService();
  return await dbService.getAll<TableLayout>('tableLayouts');
}

// Get active layout
export async function getActiveLayout(): Promise<TableLayout | null> {
  try {
    await initializeTableService();
    console.log('Getting active layout...');
    const activeLayouts = await dbService.getByIndex<TableLayout>('tableLayouts', 'isActive', true);
    console.log('Active layouts found:', activeLayouts);
    return activeLayouts.length > 0 ? activeLayouts[0] : null;
  } catch (error) {
    console.error('Error in getActiveLayout:', error);

    // Try to get all layouts as fallback
    try {
      console.log('Attempting fallback: getting all layouts...');
      const allLayouts = await dbService.getAll<TableLayout>('tableLayouts');
      console.log('All layouts:', allLayouts);
      const activeLayout = allLayouts.find(layout => layout.isActive);
      return activeLayout || null;
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
      throw error; // Re-throw original error
    }
  }
}

// Get layout by ID
export async function getLayoutById(layoutId: string): Promise<TableLayout | null> {
  await initializeTableService();
  return await dbService.get<TableLayout>('tableLayouts', layoutId);
}

// Save table layout
export async function saveTableLayout(layout: TableLayout): Promise<void> {
  await initializeTableService();
  await dbService.put('tableLayouts', layout);
}

// Set active layout
export async function setActiveLayout(layoutId: string): Promise<void> {
  await initializeTableService();

  // First, set all layouts to inactive
  const allLayouts = await getAllTableLayouts();
  for (const layout of allLayouts) {
    if (layout.isActive) {
      await dbService.put('tableLayouts', { ...layout, isActive: false });
    }
  }

  // Then set the specified layout as active
  const targetLayout = await getLayoutById(layoutId);
  if (targetLayout) {
    await dbService.put('tableLayouts', { ...targetLayout, isActive: true });

    // Generate dining tables from layout tables
    await generateDiningTablesFromLayout(targetLayout);
  }
}

// Generate dining tables from layout
async function generateDiningTablesFromLayout(layout: TableLayout): Promise<void> {
  // Remove existing dining tables for this layout
  const existingTables = await dbService.getByIndex<DiningTable>('diningTables', 'layoutId', layout.id);
  for (const table of existingTables) {
    await dbService.delete('diningTables', table.id);
  }

  // Create new dining tables from layout tables
  const diningTables: DiningTable[] = layout.tables.map(layoutTable => ({
    id: layoutTable.id,
    number: layoutTable.number,
    seats: layoutTable.seats,
    status: 'available' as const,
    layoutId: layout.id
  }));

  await saveDiningTables(diningTables);
}

// Delete table layout
export async function deleteTableLayout(layoutId: string): Promise<void> {
  await initializeTableService();

  // Remove associated dining tables
  const associatedTables = await dbService.getByIndex<DiningTable>('diningTables', 'layoutId', layoutId);
  for (const table of associatedTables) {
    await dbService.delete('diningTables', table.id);
  }

  // Remove the layout
  await dbService.delete('tableLayouts', layoutId);
}

// ===== DINING TABLE OPERATIONS =====

// Get table's current order
export async function getTableCurrentOrder(tableId: number): Promise<Order | null> {
  await initializeTableService();

  const table = await dbService.get<DiningTable>('diningTables', tableId);
  if (!table || !table.currentOrderId) {
    return null;
  }

  return await dbService.get<Order>('orders', table.currentOrderId);
}

// Link order to table (for dine-in)
export async function linkOrderToTable(orderId: string, tableId: number): Promise<void> {
  await initializeTableService();

  // Update table with current order
  await updateDiningTableStatus(tableId, 'occupied', orderId);

  // Update order with table information
  const order = await dbService.get<Order>('orders', orderId);
  if (order) {
    const table = await dbService.get<DiningTable>('diningTables', tableId);
    const updatedOrder: Order = {
      ...order,
      tableId,
      tableName: table ? `Table ${table.number}` : `Table ${tableId}`
    };
    await dbService.put('orders', updatedOrder);
  }
}

// Clear table (when order is completed/cancelled)
export async function clearTable(tableId: number): Promise<void> {
  await initializeTableService();

  const table = await dbService.get<DiningTable>('diningTables', tableId);
  if (table) {
    const updatedTable: DiningTable = {
      ...table,
      status: 'available',
      currentOrderId: undefined
    };
    await dbService.put('diningTables', updatedTable);
  }
}

// Get available tables
export async function getAvailableTables(): Promise<DiningTable[]> {
  await initializeTableService();
  return await dbService.getByIndex<DiningTable>('diningTables', 'status', 'available');
}

// Get occupied tables
export async function getOccupiedTables(): Promise<DiningTable[]> {
  await initializeTableService();
  return await dbService.getByIndex<DiningTable>('diningTables', 'status', 'occupied');
}

// Auto-save order to table (for dine-in orders)
export async function autoSaveOrderToTable(
  tableId: number,
  items: OrderItem[],
  customerInfo: any,
  discount: number = 0,
  discountType: 'percentage' | 'fixed' = 'percentage'
): Promise<string> {
  await initializeTableService();
  
  // Check if table already has an order
  const existingOrder = await getTableCurrentOrder(tableId);
  
  if (existingOrder) {
    // Update existing order
    const updatedOrder: Order = {
      ...existingOrder,
      items: [...items], // Replace items with current cart
      customerInfo,
      updatedAt: Date.now()
    };
    
    await dbService.put('orders', updatedOrder);
    return existingOrder.id;
  } else {
    // Create new draft order
    const orderId = Date.now().toString(36) + Math.random().toString(36).substr(2);
    const now = Date.now();
    const date = new Date(now);
    
    const newOrder: Order = {
      id: orderId,
      orderNumber: `DRAFT-${tableId}-${Date.now()}`,
      invoiceId: '', // Will be set during checkout
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString(),
      orderType: 'dine-in',
      customerInfo,
      items: [...items],
      total: 0, // Will be calculated during checkout
      status: 'pending',
      createdBy: 'Auto-save',
      createdAt: now,
      updatedAt: now,
      tableId,
      tableName: `Table ${tableId}`
    };
    
    await dbService.put('orders', newOrder);
    await linkOrderToTable(orderId, tableId);
    
    return orderId;
  }
}

// Load order from table
export async function loadOrderFromTable(tableId: number): Promise<{
  order: Order | null;
  items: OrderItem[];
}> {
  await initializeTableService();
  
  const order = await getTableCurrentOrder(tableId);
  
  if (order) {
    return {
      order,
      items: order.items || []
    };
  }
  
  return {
    order: null,
    items: []
  };
}

// Get table statistics
export async function getTableStatistics(): Promise<{
  totalTables: number;
  availableTables: number;
  occupiedTables: number;
  reservedTables: number;
  occupancyRate: number;
}> {
  await initializeTableService();

  const tables = await getActiveDiningTables();
  const available = tables.filter(t => t.status === 'available').length;
  const occupied = tables.filter(t => t.status === 'occupied').length;
  const reserved = tables.filter(t => t.status === 'reserved').length;

  return {
    totalTables: tables.length,
    availableTables: available,
    occupiedTables: occupied,
    reservedTables: reserved,
    occupancyRate: tables.length > 0 ? (occupied / tables.length) * 100 : 0
  };
}

// Initialize default layout and tables if none exist
export async function initializeDefaultTables(): Promise<void> {
  try {
    console.log('Initializing default tables...');
    await initializeTableService();

    const existingLayouts = await getAllTableLayouts();
    console.log('Existing layouts:', existingLayouts);

    if (existingLayouts.length === 0) {
      console.log('No existing layouts found, creating default layout...');
      // Create default layout
      const defaultLayout: TableLayout = {
        id: 'default-layout',
        name: 'Default Layout',
        isActive: true,
        tables: [
          { id: 1, x: 100, y: 100, width: 80, height: 80, number: 1, seats: 4, status: 'available', borderColor: '#16a34a' },
          { id: 2, x: 200, y: 100, width: 80, height: 80, number: 2, seats: 4, status: 'available', borderColor: '#16a34a' },
          { id: 3, x: 300, y: 100, width: 80, height: 80, number: 3, seats: 4, status: 'available', borderColor: '#16a34a' },
          { id: 4, x: 400, y: 100, width: 80, height: 80, number: 4, seats: 4, status: 'available', borderColor: '#16a34a' },
          { id: 5, x: 100, y: 200, width: 120, height: 80, number: 5, seats: 6, status: 'available', borderColor: '#16a34a' },
          { id: 6, x: 250, y: 200, width: 120, height: 80, number: 6, seats: 6, status: 'available', borderColor: '#16a34a' },
        ],
        floorPlanImage: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await saveTableLayout(defaultLayout);
      console.log('Default layout saved, generating dining tables...');
      await generateDiningTablesFromLayout(defaultLayout);
      console.log('Default tables initialized successfully');
    } else {
      console.log('Existing layouts found, skipping default creation');
    }
  } catch (error) {
    console.error('Error initializing default tables:', error);
    throw error;
  }
}

// Backward compatibility - use active dining tables
export async function getAllTables(): Promise<DiningTable[]> {
  return await getActiveDiningTables();
}
