import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Plus, Settings, Save, Upload, RotateCcw, Trash2, Database, FolderOpen } from 'lucide-react';
import { TableLayout, LayoutTable } from '../types';
import {
  getAllLayouts,
  getActiveLayout,
  saveLayout,
  createLayout,
  setActiveLayout,
  deleteLayout
} from '../services/layoutService';
import { initializeDefaultTables } from '../services/tableService';

// Types for backward compatibility
type TableStatus = 'available' | 'occupied' | 'reserved';

// Removed old IndexedDB functions - now using unified layoutService

const getStatusColor = (status: TableStatus): string => {
  switch (status) {
    case 'occupied': return '#dc2626';
    case 'reserved': return '#ea580c';
    default: return '#16a34a';
  }
};

interface DraggableResizableTableProps {
  table: LayoutTable;
  onUpdate: (id: number, updates: Partial<LayoutTable>) => void;
  onDelete: (id: number) => void;
  isSelected: boolean;
  onSelect: (id: number) => void;
}

const DraggableResizableTable: React.FC<DraggableResizableTableProps> = ({ 
  table, 
  onUpdate, 
  onDelete, 
  isSelected, 
  onSelect 
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [resizeStart, setResizeStart] = useState({ 
    width: 0, 
    height: 0, 
    mouseX: 0, 
    mouseY: 0 
  });
  const tableRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if ((e.target as HTMLElement).classList.contains('resize-handle')) {
      setIsResizing(true);
      setResizeStart({
        width: table.width,
        height: table.height,
        mouseX: e.clientX,
        mouseY: e.clientY
      });
      e.stopPropagation();
      e.preventDefault();
    } else {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - table.x,
        y: e.clientY - table.y
      });
      onSelect(table.id);
      e.preventDefault();
    }
  }, [table, onSelect]);

  const handleResizeMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true);
    setResizeStart({
      width: table.width,
      height: table.height,
      mouseX: e.clientX,
      mouseY: e.clientY
    });
    onSelect(table.id);
    e.stopPropagation();
    e.preventDefault();
  }, [table, onSelect]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging) {
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;
      onUpdate(table.id, { x: Math.max(0, newX), y: Math.max(0, newY) });
    } else if (isResizing) {
      const deltaX = e.clientX - resizeStart.mouseX;
      const deltaY = e.clientY - resizeStart.mouseY;
      const newWidth = Math.max(60, resizeStart.width + deltaX);
      const newHeight = Math.max(60, resizeStart.height + deltaY);
      onUpdate(table.id, { width: newWidth, height: newHeight });
    }
  }, [isDragging, isResizing, dragStart, resizeStart, table.id, onUpdate]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  return (
    <div
      ref={tableRef}
      className={`absolute cursor-move select-none ${
        isSelected ? 'z-20' : 'z-10'
      } ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
      style={{
        left: table.x,
        top: table.y,
        width: table.width,
        height: table.height,
      }}
      onMouseDown={handleMouseDown}
    >
      {/* Table */}
      <div
        className={`
          w-full h-full rounded-lg border-4 flex items-center justify-center
          font-bold text-lg shadow-lg bg-transparent backdrop-blur-sm
          ${isSelected ? 'ring-4 ring-blue-500' : ''}
          hover:shadow-xl transition-all
        `}
        style={{
          borderColor: table.borderColor,
          color: table.borderColor
        }}
      >
        <div className="text-center">
          <div className="text-xl font-bold">{table.number}</div>
          <div className="text-xs opacity-80">{table.seats} seats</div>
        </div>
      </div>

      {/* Resize Handle - only show when selected */}
      {isSelected && (
        <div
          className="resize-handle absolute bottom-0 right-0 w-4 h-4 bg-blue-500 
                     cursor-se-resize border-2 border-white rounded-tl-lg shadow-md
                     hover:bg-blue-600 transition-colors"
          style={{ transform: 'translate(50%, 50%)' }}
          onMouseDown={handleResizeMouseDown}
        />
      )}

      {/* Delete button - only show when selected */}
      {isSelected && (
        <button
          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white 
                     rounded-full flex items-center justify-center hover:bg-red-600
                     transition-colors shadow-md"
          onClick={(e) => {
            e.stopPropagation();
            onDelete(table.id);
          }}
        >
          <Trash2 className="w-3 h-3" />
        </button>
      )}

      {/* Status indicator */}
      <div 
        className="absolute -top-1 -left-1 w-3 h-3 rounded-full border border-white"
        style={{ backgroundColor: table.borderColor }}
      />
    </div>
  );
};

interface SaveLayoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string) => Promise<void>;
  currentLayout: { tables: LayoutTable[]; floorPlanImage: string | null };
}

const SaveLayoutModal: React.FC<SaveLayoutModalProps> = ({ 
  isOpen, 
  onClose, 
  onSave, 
  currentLayout 
}) => {
  const [layoutName, setLayoutName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!layoutName.trim()) return;
    
    setIsLoading(true);
    try {
      await onSave(layoutName.trim());
      setLayoutName('');
      onClose();
    } catch (error) {
      console.error('Failed to save layout:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96">
        <h3 className="text-lg font-semibold mb-4">Save Layout</h3>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Layout Name
          </label>
          <input
            type="text"
            value={layoutName}
            onChange={(e) => setLayoutName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter layout name..."
            onKeyPress={(e) => e.key === 'Enter' && handleSave()}
          />
        </div>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!layoutName.trim() || isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  );
};

interface LoadLayoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoad: (layout: TableLayout) => Promise<void>;
}

const LoadLayoutModal: React.FC<LoadLayoutModalProps> = ({ 
  isOpen, 
  onClose, 
  onLoad 
}) => {
  const [layouts, setLayouts] = useState<TableLayout[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadLayouts();
    }
  }, [isOpen]);

  const loadLayouts = async () => {
    setIsLoading(true);
    try {
      const savedLayouts = await getAllLayouts();
      setLayouts(savedLayouts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));
    } catch (error) {
      console.error('Failed to load layouts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoad = async (layout: TableLayout) => {
    try {
      await onLoad(layout);
      onClose();
    } catch (error) {
      console.error('Failed to load layout:', error);
    }
  };

  const handleDelete = async (layoutId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await deleteLayout(layoutId);
      setLayouts(prev => prev.filter(layout => layout.id !== layoutId));
    } catch (error) {
      console.error('Failed to delete layout:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96 max-h-96">
        <h3 className="text-lg font-semibold mb-4">Load Layout</h3>
        
        {isLoading ? (
          <div className="text-center py-8">Loading layouts...</div>
        ) : layouts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No saved layouts found
          </div>
        ) : (
          <div className="max-h-64 overflow-y-auto mb-4">
            {layouts.map(layout => (
              <div
                key={layout.id}
                className="p-3 border border-gray-200 rounded-lg mb-2 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => handleLoad(layout)}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">{layout.name}</div>
                    <div className="text-sm text-gray-500">
                      {layout.tables.length} tables • {new Date(layout.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <button
                    onClick={(e) => handleDelete(layout.id, e)}
                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
        
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

const TableOverlayManager: React.FC = () => {
  const [tables, setTables] = useState<LayoutTable[]>([
    { id: 1, x: 100, y: 100, width: 80, height: 80, number: 1, seats: 4, status: 'available', borderColor: '#16a34a' },
    { id: 2, x: 200, y: 150, width: 120, height: 80, number: 2, seats: 6, status: 'occupied', borderColor: '#dc2626' },
    { id: 3, x: 350, y: 100, width: 80, height: 80, number: 3, seats: 4, status: 'reserved', borderColor: '#ea580c' },
  ]);

  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [nextTableId, setNextTableId] = useState(4);
  const [floorPlanImage, setFloorPlanImage] = useState<string | null>(null);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [currentLayoutName, setCurrentLayoutName] = useState('Untitled Layout');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize layout service on component mount
  useEffect(() => {
    initializeDefaultTables().catch(console.error);
    loadActiveLayout();
  }, []);

  const loadActiveLayout = async () => {
    try {
      const activeLayout = await getActiveLayout();
      if (activeLayout) {
        setTables(activeLayout.tables);
        setFloorPlanImage(activeLayout.floorPlanImage);
        setCurrentLayoutName(activeLayout.name);
        setNextTableId(Math.max(...activeLayout.tables.map(t => t.id)) + 1);
      }
    } catch (error) {
      console.error('Failed to load active layout:', error);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setFloorPlanImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const updateTable = useCallback((id: number, updates: Partial<LayoutTable>) => {
    setTables(prev => prev.map(table =>
      table.id === id ? { ...table, ...updates } : table
    ));
  }, []);

  const deleteTable = useCallback((id: number) => {
    setTables(prev => prev.filter(table => table.id !== id));
    setSelectedTable(null);
  }, []);

  const addNewTable = useCallback((e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left - 40; // Center the table on click
    const y = e.clientY - rect.top - 40;

    const newTable: LayoutTable = {
      id: nextTableId,
      x: Math.max(0, x),
      y: Math.max(0, y),
      width: 80,
      height: 80,
      number: nextTableId,
      seats: 4,
      status: 'available',
      borderColor: '#16a34a'
    };

    setTables(prev => [...prev, newTable]);
    setNextTableId(prev => prev + 1);
    setSelectedTable(newTable.id);
  }, [nextTableId]);

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    // Only add table if clicking on empty space (not on existing table)
    if (e.target === e.currentTarget) {
      addNewTable(e);
    } else {
      setSelectedTable(null);
    }
  }, [addNewTable]);

  const saveLayoutCallback = useCallback(async (name: string) => {
    const layout = await createLayout(name, tables, floorPlanImage);
    await setActiveLayout(layout.id);
    setCurrentLayoutName(name);
  }, [tables, floorPlanImage]);

  const loadLayout = useCallback(async (layout: TableLayout) => {
    setTables(layout.tables || []);
    setFloorPlanImage(layout.floorPlanImage || null);
    setCurrentLayoutName(layout.name);
    setSelectedTable(null);

    // Set as active layout
    await setActiveLayout(layout.id);

    // Update nextTableId to avoid conflicts
    const maxId = Math.max(0, ...(layout.tables || []).map(t => t.id));
    setNextTableId(maxId + 1);
  }, []);

  const createNewLayout = useCallback(() => {
    setTables([]);
    setFloorPlanImage(null);
    setCurrentLayoutName('Untitled Layout');
    setSelectedTable(null);
    setNextTableId(1);
  }, []);

  const selectedTableData = tables.find(t => t.id === selectedTable);

  return (
    <div className="h-screen bg-gray-100 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold text-gray-800">Table Layout Manager</h1>
          <span className="text-sm text-gray-500">({currentLayoutName})</span>
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            <Upload className="w-4 h-4" />
            <span>Upload Floor Plan</span>
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
          />
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={createNewLayout}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            <Plus className="w-4 h-4 inline mr-2" />
            New
          </button>
          <button
            onClick={() => setShowLoadModal(true)}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          >
            <FolderOpen className="w-4 h-4 inline mr-2" />
            Load
          </button>
          <button
            onClick={() => setShowSaveModal(true)}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            <Save className="w-4 h-4 inline mr-2" />
            Save
          </button>
        </div>
      </div>

      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto">
          <div className="space-y-6">
            {/* Instructions */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-800 mb-2">How to use:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Click empty space to add new table</li>
                <li>• Drag tables to move them</li>
                <li>• Drag resize handle to change size</li>
                <li>• Click table to select/deselect</li>
                <li>• Use delete button to remove tables</li>
                <li>• Save/load layouts using buttons above</li>
              </ul>
            </div>

            {/* Table List */}
            <div>
              <h3 className="font-medium text-gray-700 mb-3">Tables ({tables.length})</h3>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {tables.map(table => (
                  <div
                    key={table.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedTable === table.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedTable(table.id)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">Table {table.number}</div>
                        <div className="text-sm text-gray-500">
                          {table.seats} seats • {table.status}
                        </div>
                      </div>
                      <div className={`w-3 h-3 rounded-full border border-white`} 
                           style={{ backgroundColor: table.borderColor }} />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Table Properties */}
            {selectedTableData && (
              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-700 mb-3">Table Properties</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">
                      Table Number
                    </label>
                    <input
                      type="number"
                      value={selectedTableData.number}
                      onChange={(e) => updateTable(selectedTable, { number: parseInt(e.target.value) || 1 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">
                      Seats
                    </label>
                    <input
                      type="number"
                      value={selectedTableData.seats}
                      onChange={(e) => updateTable(selectedTable, { seats: parseInt(e.target.value) || 1 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">
                      Status
                    </label>
                    <select
                      value={selectedTableData.status}
                      onChange={(e) => {
                        const newStatus = e.target.value as TableStatus;
                        const newBorderColor = getStatusColor(newStatus);
                        updateTable(selectedTable, { status: newStatus, borderColor: newBorderColor });
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="available">Available</option>
                      <option value="occupied">Occupied</option>
                      <option value="reserved">Reserved</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">
                      Border Color
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        value={selectedTableData.borderColor}
                        onChange={(e) => updateTable(selectedTable, { borderColor: e.target.value })}
                        className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={selectedTableData.borderColor}
                        onChange={(e) => updateTable(selectedTable, { borderColor: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                        placeholder="#000000"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-600 mb-1">
                        Width
                      </label>
                      <input
                        type="number"
                        value={selectedTableData.width}
                        onChange={(e) => updateTable(selectedTable, { width: parseInt(e.target.value) || 60 })}
                        className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-600 mb-1">
                        Height
                      </label>
                      <input
                        type="number"
                        value={selectedTableData.height}
                        onChange={(e) => updateTable(selectedTable, { height: parseInt(e.target.value) || 60 })}
                        className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <button
                    onClick={() => deleteTable(selectedTable)}
                    className="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                  >
                    <Trash2 className="w-4 h-4 inline mr-2" />
                    Delete Table
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Main Canvas */}
        <div className="flex-1 relative overflow-auto">
          <div
            className="min-w-full min-h-full relative cursor-crosshair"
            onClick={handleCanvasClick}
            style={{
              backgroundImage: floorPlanImage ? `url(${floorPlanImage})` : 'none',
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'left top',
              minHeight: '800px',
              minWidth: '1200px'
            }}
          >
            {/* Grid overlay when no image */}
            {!floorPlanImage && (
              <div 
                className="absolute inset-0 opacity-10"
                style={{
                  backgroundImage: `
                    linear-gradient(rgba(0,0,0,0.3) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(0,0,0,0.3) 1px, transparent 1px)
                  `,
                  backgroundSize: '20px 20px'
                }}
              />
            )}

            {/* Instructions overlay when no image */}
            {!floorPlanImage && (
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="text-center text-gray-400">
                  <Upload className="w-16 h-16 mx-auto mb-4" />
                  <p className="text-xl mb-2">Upload a floor plan image</p>
                  <p className="text-sm">Or click anywhere to start placing tables</p>
                </div>
              </div>
            )}

            {/* Tables */}
            {tables.map(table => (
              <DraggableResizableTable
                key={table.id}
                table={table}
                onUpdate={updateTable}
                onDelete={deleteTable}
                isSelected={selectedTable === table.id}
                onSelect={setSelectedTable}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Modals */}
      <SaveLayoutModal
        isOpen={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        onSave={saveLayoutCallback}
        currentLayout={{ tables, floorPlanImage }}
      />
      
      <LoadLayoutModal
        isOpen={showLoadModal}
        onClose={() => setShowLoadModal(false)}
        onLoad={loadLayout}
      />
    </div>
  );
};

export default TableOverlayManager;